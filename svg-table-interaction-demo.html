<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG Table Interaction Demo</title>
    
    <!-- Include the CSS files -->
    <link rel="stylesheet" href="assets/css/svg-table-interaction.css">
    
    <style>
        /* Demo-specific styles */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .demo-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .demo-header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .demo-content {
            padding: 30px;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .demo-section h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .demo-instructions {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }
        
        .demo-instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .demo-instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .demo-instructions li {
            margin-bottom: 8px;
        }
        
        .interactive-demo {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        
        .svg-container {
            position: relative;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .svg-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-container {
            margin-top: 30px;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        
        .feature-highlight h4 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        .code-example .comment {
            color: #a0aec0;
        }
        
        .code-example .keyword {
            color: #fbb6ce;
        }
        
        .code-example .string {
            color: #9ae6b4;
        }
        
        .code-example .function {
            color: #90cdf4;
        }
        
        .footer {
            background: #343a40;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>SVG Table Interaction Demo</h1>
            <p>Interactive land plot map with synchronized table navigation</p>
        </div>
        
        <div class="demo-content">
            <div class="demo-section">
                <h2>How It Works</h2>
                <div class="demo-instructions">
                    <h3>Interactive Features:</h3>
                    <ul>
                        <li><strong>Hover Effects:</strong> Table rows highlight on hover with smooth animations</li>
                        <li><strong>Click Navigation:</strong> Click on any SVG square to scroll to the corresponding table row</li>
                        <li><strong>Visual Feedback:</strong> SVG squares provide visual feedback when clicked</li>
                        <li><strong>Responsive Design:</strong> Works seamlessly on all device sizes</li>
                        <li><strong>Accessibility:</strong> Keyboard navigation and focus states included</li>
                    </ul>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>Interactive Demo</h2>
                <div class="interactive-demo">
                    <div class="svg-container">
                        <p><strong>Interactive Map:</strong> Click on any colored square to navigate to its details in the table below</p>
                        <!-- Your existing SVG map would go here -->
                        <div style="background: #e9ecef; padding: 40px; border-radius: 8px; text-align: center;">
                            <p style="margin: 0; color: #6c757d;">SVG Map Placeholder</p>
                            <p style="margin: 10px 0 0 0; font-size: 0.9rem; color: #adb5bd;">
                                Your existing SVG map with class="magi-square" elements will be displayed here
                            </p>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <h3>Land Plot Details</h3>
                        <p><strong>Instructions:</strong> Hover over table rows to see hover effects, or click on map squares above to navigate here</p>
                        
                        <!-- Sample table structure -->
                        <table class="rv_tb-tablebuilder">
                            <tbody>
                                <tr>
                                    <td class="rv_tb-cell"><strong>Aadress</strong></td>
                                    <td class="rv_tb-cell"><strong>Pindala (M²)</strong></td>
                                    <td class="rv_tb-cell"><strong>Hind</strong></td>
                                </tr>
                                <tr>
                                    <td class="rv_tb-cell">Mägimänni tee 4</td>
                                    <td class="rv_tb-cell">3310M²</td>
                                    <td class="rv_tb-cell">135000€</td>
                                </tr>
                                <tr>
                                    <td class="rv_tb-cell">Mägimänni tee 6</td>
                                    <td class="rv_tb-cell">2025M²</td>
                                    <td class="rv_tb-cell">155000€</td>
                                </tr>
                                <tr>
                                    <td class="rv_tb-cell">Nulu tee 1</td>
                                    <td class="rv_tb-cell">1780M²</td>
                                    <td class="rv_tb-cell">160000€</td>
                                </tr>
                                <tr>
                                    <td class="rv_tb-cell">Nulu tee 3</td>
                                    <td class="rv_tb-cell">2001M²</td>
                                    <td class="rv_tb-cell">160000€</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>Implementation</h2>
                
                <div class="feature-highlight">
                    <h4>🚀 Easy Integration</h4>
                    <p>Simply include the JavaScript and CSS files in your WordPress theme, and the functionality will work automatically with your existing SVG map and tables.</p>
                </div>
                
                <h3>Files to Include:</h3>
                <div class="code-example">
<span class="comment">// In your WordPress theme's functions.php or header.php</span>
<span class="keyword">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="assets/css/svg-table-interaction.css"</span><span class="keyword">&gt;</span>
<span class="keyword">&lt;script</span> <span class="string">src="assets/js/svg-table-interaction.js"</span><span class="keyword">&gt;&lt;/script&gt;</span>
                </div>
                
                <h3>Required HTML Structure:</h3>
                <div class="code-example">
<span class="comment">// SVG squares must have class="magi-square" and unique IDs</span>
<span class="keyword">&lt;path</span> <span class="string">class="magi-square"</span> <span class="string">id="Nulu_tee_10"</span> <span class="string">d="..."</span><span class="keyword">&gt;&lt;/path&gt;</span>

<span class="comment">// Table rows should be in standard table structure</span>
<span class="keyword">&lt;table&gt;</span>
    <span class="keyword">&lt;tbody&gt;</span>
        <span class="keyword">&lt;tr&gt;</span>
            <span class="keyword">&lt;td&gt;</span><span class="string">Nulu tee 10</span><span class="keyword">&lt;/td&gt;</span>
        <span class="keyword">&lt;/tr&gt;</span>
    <span class="keyword">&lt;/tbody&gt;</span>
<span class="keyword">&lt;/table&gt;</span>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>Features</h2>
                
                <div class="feature-highlight">
                    <h4>✨ Enhanced User Experience</h4>
                    <ul>
                        <li>Smooth hover animations on table rows</li>
                        <li>Click-to-navigate from map to table</li>
                        <li>Visual feedback and highlighting</li>
                        <li>Responsive design for all devices</li>
                        <li>Accessibility features included</li>
                    </ul>
                </div>
                
                <div class="feature-highlight">
                    <h4>🔧 Technical Features</h4>
                    <ul>
                        <li>Automatic ID matching between SVG and table</li>
                        <li>Smooth scrolling animations</li>
                        <li>CSS transitions and transforms</li>
                        <li>Event handling for all interactions</li>
                        <li>Cross-browser compatibility</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>SVG Table Interaction Demo | Built for WordPress Real Estate Themes</p>
        <p>Include the JavaScript and CSS files to enable this functionality on your site</p>
    </div>
    
    <!-- Include the JavaScript file -->
    <script src="assets/js/svg-table-interaction.js"></script>
</body>
</html>
