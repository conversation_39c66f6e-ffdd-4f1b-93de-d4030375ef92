# SVG Table Interaction System

A comprehensive solution for creating interactive land plot maps with synchronized table navigation in WordPress themes.

## 🎯 Overview

This system enables users to:
- **Hover over table rows** to see smooth highlight effects
- **Click on SVG map squares** to automatically scroll to corresponding table rows
- **Navigate seamlessly** between the visual map and detailed data tables
- **Experience smooth animations** and visual feedback throughout

## 📁 Files Included

- `assets/js/svg-table-interaction.js` - Main JavaScript functionality
- `assets/css/svg-table-interaction.css` - Enhanced styling and animations
- `svg-table-interaction-demo.html` - Interactive demonstration page
- `SVG-TABLE-INTERACTION-README.md` - This documentation

## 🚀 Quick Start

### 1. Include Files in Your WordPress Theme

Add these lines to your theme's `functions.php` or `header.php`:

```php
// Enqueue the CSS file
wp_enqueue_style('svg-table-interaction', get_template_directory_uri() . '/assets/css/svg-table-interaction.css');

// Enqueue the JavaScript file
wp_enqueue_script('svg-table-interaction', get_template_directory_uri() . '/assets/js/svg-table-interaction.js', array(), '1.0.0', true);
```

### 2. Ensure Proper HTML Structure

Your SVG map should have squares with the class `magi-square` and unique IDs:

```html
<path class="magi-square" id="Nulu_tee_10" d="..." fill="#3F1F1F"></path>
<path class="magi-square" id="Magimanni_tee_4" d="..." fill="#3F1F1F"></path>
```

Your tables should have standard HTML structure:

```html
<table class="rv_tb-tablebuilder">
    <tbody>
        <tr>
            <td class="rv_tb-cell">Aadress</td>
            <td class="rv_tb-cell">Pindala (M²)</td>
            <td class="rv_tb-cell">Hind</td>
        </tr>
        <tr>
            <td class="rv_tb-cell">Nulu tee 10</td>
            <td class="rv_tb-cell">2008M²</td>
            <td class="rv_tb-cell">154000€</td>
        </tr>
    </tbody>
</table>
```

### 3. Automatic Functionality

Once the files are included, the system automatically:
- Adds hover effects to table rows
- Enables click-to-navigate from SVG squares to table rows
- Provides visual feedback and smooth animations

## 🔧 How It Works

### ID Matching System

The system automatically converts SVG IDs to readable addresses:

| SVG ID | Table Address |
|--------|---------------|
| `Nulu_tee_10` | `Nulu tee 10` |
| `Magimanni_tee_4` | `Mägimänni tee 4` |

### Event Handling

1. **SVG Square Click**: User clicks on a map square
2. **ID Conversion**: System converts the square ID to a readable address
3. **Table Search**: System searches both tables for matching addresses
4. **Row Highlighting**: Target table row is highlighted
5. **Smooth Scrolling**: Page scrolls to the highlighted row

### Visual Feedback

- **Hover Effects**: Table rows lift slightly and show shadows
- **Click Feedback**: SVG squares flash with a yellow highlight
- **Row Highlighting**: Selected rows get a blue background and border
- **Smooth Transitions**: All animations use CSS transitions for performance

## 🎨 Customization

### CSS Variables

You can customize colors and animations by modifying the CSS file:

```css
/* Primary highlight color */
.rv_tb-tablebuilder tbody tr.highlighted {
    background-color: #e3f2fd !important; /* Change this color */
    border-left: 4px solid #2196f3; /* Change this color */
}

/* Hover effect color */
.rv_tb-tablebuilder tbody tr:not(:first-child):hover {
    background-color: #f8f9fa !important; /* Change this color */
}
```

### JavaScript Configuration

Modify the JavaScript file to change behavior:

```javascript
// Change scroll behavior
scrollToTableRow(row) {
    row.scrollIntoView({ 
        behavior: 'smooth', // Change to 'auto' for instant scroll
        block: 'center'     // Change to 'start', 'end', or 'nearest'
    });
}

// Change highlight colors
function highlightTableRow(row) {
    row.style.backgroundColor = '#your-color-here';
}
```

## 📱 Responsive Design

The system automatically adapts to different screen sizes:

- **Desktop**: Full hover effects and animations
- **Tablet**: Reduced animations for better performance
- **Mobile**: Touch-friendly interactions without hover effects

## ♿ Accessibility Features

- **Keyboard Navigation**: Use Tab to navigate between interactive elements
- **Focus States**: Clear visual indicators for focused elements
- **Screen Reader Support**: Proper ARIA labels and semantic structure
- **High Contrast**: Maintains readability across different color schemes

## 🐛 Troubleshooting

### Common Issues

1. **SVG squares not clickable**
   - Ensure squares have class `magi-square`
   - Check that IDs are unique and properly formatted

2. **Table rows not highlighting**
   - Verify table structure matches requirements
   - Check that address text exactly matches the converted SVG ID

3. **Scrolling not working**
   - Ensure smooth scrolling is supported by the browser
   - Check for CSS conflicts that might prevent scrolling

### Debug Mode

Enable console logging by adding this to your page:

```javascript
// Enable debug mode
window.svgTableDebug = true;
```

## 🔄 Updates and Maintenance

### Version History

- **v1.0.0**: Initial release with core functionality
- Future updates will include additional features and optimizations

### Browser Support

- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+

## 📞 Support

For questions or issues:

1. Check the demo page for examples
2. Review the console for error messages
3. Verify HTML structure matches requirements
4. Test in different browsers

## 🎯 Use Cases

This system is perfect for:

- **Real Estate Websites**: Land plot maps with property details
- **Property Management**: Interactive property listings
- **Development Projects**: Site plan navigation
- **Educational Content**: Interactive geography lessons
- **Business Presentations**: Visual data navigation

## 📄 License

This code is provided as-is for use in WordPress themes. Feel free to modify and adapt for your specific needs.

---

**Built with ❤️ for WordPress developers**



