/**
 * SVG Table Interaction Styles
 * Enhanced styling for the interactive SVG map and table functionality
 */

/* Enhanced table styling */
.rv_tb-tablebuilder table {
    border-collapse: collapse;
    width: 100%;
    margin: 20px 0;
}

.rv_tb-tablebuilder tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid #e0e0e0;
}

.rv_tb-tablebuilder tbody tr:not(:first-child):hover {
    background-color: #f8f9fa !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.rv_tb-tablebuilder tbody tr:not(:first-child):active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Highlighted row state */
.rv_tb-tablebuilder tbody tr.highlighted {
    background-color: #2f5d50 !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
    border-left: 4px solid #2196f3;
}

/* Enhanced SVG square interactions */
.magi-square {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    fill-opacity: 0.6 !important;
}

.magi-square path {
    fill-opacity: 0.6 !important;
}

.magi-square:hover {
    filter: brightness(1.1) contrast(1.1);
    transform: scale(1.02);
    stroke-width: 3;
    fill-opacity: 0.6 !important;
}

.magi-square:hover path {
    fill-opacity: 0.6 !important;
}

.magi-square:active {
    transform: scale(0.98);
    filter: brightness(0.9);
    fill-opacity: 0.6 !important;
}

.magi-square:active path {
    fill-opacity: 0.6 !important;
}

/* Click feedback animation */
.magi-square.clicked {
    animation: squareClickPulse 0.5s ease-out;
}

@keyframes squareClickPulse {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }

    50% {
        transform: scale(1.1);
        filter: brightness(1.3);
    }

    100% {
        transform: scale(1);
        filter: brightness(1);
    }
}

/* Enhanced badge hover effects */
.magi-badge {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.magi-badge:hover {
    transform: translateY(-5px) scale(1.05);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
}

/* Focus states for accessibility */
.magi-square:focus,
.magi-badge:focus {
    outline: none !important;
    outline-offset: 0 !important;
}

/* Remove outline when square is clicked/active */
.magi-square:active,
.magi-square.clicked {
    outline: none !important;
    outline-offset: 0 !important;
}

/* Table cell enhancements */
.rv_tb-cell {
    padding: 12px 16px;
    transition: background-color 0.2s ease;
}

.rv_tb-cell:hover {
    background-color: #1eaf53;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .magi-square:hover {
        transform: none;
        filter: brightness(1.05);
    }

    .magi-badge:hover {
        transform: none;
    }

    .rv_tb-tablebuilder tbody tr:not(:first-child):hover {
        transform: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
}

/* Loading state for better UX */
.table-loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Smooth scrolling for the entire page */
html {
    scroll-behavior: smooth;
}

/* Enhanced visual hierarchy */
.rv_tb-tablebuilder {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Table header styling */
.rv_tb-tablebuilder thead th,
.rv_tb-tablebuilder tbody tr:first-child td {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #ddd;
}

/* Interactive elements focus ring */
.rv_tb-tablebuilder tbody tr:focus-within {
    outline: none !important;
    outline-offset: 0 !important;
}

/* Hover state for table rows with better contrast */
.rv_tb-tablebuilder tbody tr:not(:first-child):hover .rv_tb-cell {
    background-color: rgba(33, 150, 243, 0.08);
}

/* Active/selected state */
.rv_tb-tablebuilder tbody tr.selected {
    background-color: #22c55e !important;
    border-left: 4px solid #4caf50;
}

/* Animation for row highlighting */
@keyframes rowHighlight {
    0% {
        background-color: transparent;
    }

    50% {
        background-color: #22c55e;
    }

    100% {
        background-color: #22c55e;
    }
}

.rv_tb-tablebuilder tbody tr.highlight-animation {
    animation: rowHighlight 0.6s ease-out;
}

/* Enhanced SVG container */
#magi-new {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Tooltip styling for SVG squares */
.magi-square[title]:hover::after {
    content: attr(title);
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    transform: translateY(-100%);
    margin-top: -8px;
}

/* Enhanced table responsiveness */
@media (max-width: 480px) {
    .rv_tb-tablebuilder {
        font-size: 14px;
    }

    .rv_tb-cell {
        padding: 8px 12px;
    }
}

/* Styling for booked property badges */
.magi-badge text[data-booked="true"] {
    font-weight: 700 !important;
    fill: #ffea3b !important;
    fill-opacity: 1 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
    font-size: 8.5px !important;
}

/* Ensure booked text is visible */
.magi-badge text[data-booked="true"] tspan {
    font-weight: 700 !important;
    fill: #ffea3b !important;
    fill-opacity: 1 !important;
}

/* Target tspan elements directly when they have the data-booked attribute */
.magi-badge tspan[data-booked="true"] {
    font-weight: 700 !important;
    fill: #ffea3b !important;
    fill-opacity: 1 !important;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8) !important;
}


/* Inline CSS for testing - Magi-square hover effects - NO MOVEMENT */
.magi-square path,
.magi-square-path {
    transition: fill 0.3s ease, stroke 0.3s ease, opacity 0.3s ease;
    cursor: pointer;
    /* No transform or movement properties */
}

/* Green fill on hover - smooth color change only */
.magi-square path:hover,
.magi-square-path:hover {
    fill: #22c55e !important;
    /* Green color */
    stroke: #16a34a;
    /* Darker green stroke */
    opacity: 0.9;
    /* No transform or scale changes */
}

/* Ensure no movement on squares, but allow badge movement */
.magi-square,
.magi-square path,
.magi-square-path {
    transform: none !important;
}

.magi-square:hover,
.magi-square path:hover,
.magi-square-path:hover {
    transform: none !important;
}

/* Magi-badge hover effects */
.magi-badge {
    transition: transform 0.3s ease;
    transform: translateY(0);
}

.magi-badge.hover-up {
    transform: translateY(-10px);
}

/* Badge text - no scaling, just smooth transitions */
.magi-badge text {
    transition: opacity 0.3s ease;
}

#magi-new {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    max-width: unset;
    margin: 0;
}