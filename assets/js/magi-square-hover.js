  // Targeted hover effects - each square affects only its corresponding badge
  document.addEventListener('DOMContentLoaded', function () {
    ////console.log('DOM loaded, checking for magikas SVG...');

    // Property status configuration
    const PROPERTY_STATUS = {
        // Available properties (default green on hover)
        available: [],
        
        // Booked properties (orange/brown color)
        booked: [
            'Nulu_tee_2', 'Nulu_tee_4', 'Nulu_tee_6', 'Nulu_tee_8', 'Nulu_tee_10', 'Nulu_tee_12',
             'Magimanni_tee_10', 'Nulu_tee_1', 'Nulu_tee_3','Magimanni_tee_11'
        ],
        
        // Sold properties (red color)
        sold: ['Nulu_tee_5']
    };

    // Status colors
    const STATUS_COLORS = {
        available: {
            hover: '#22c55e',      // Green
            stroke: '#16a34a',     // Darker green
            original: '#3F1F1F'    // Original brown
        },
        booked: {
            hover: '#f97316',      // Orange
            stroke: '#ea580c',     // Darker orange
            original: '#92400e'    // Darker brown
        },
        sold: {
            hover: '#ef4444',      // Red
            stroke: '#dc2626',     // Darker red
            original: '#7f1d1d'    // Dark red-brown
        }
    };

    const magikasSVG = document.getElementById('magikas');
    //console.log('Magikas SVG found:', magikasSVG);

    if (magikasSVG) {
        const magiSquarePaths = magikasSVG.querySelectorAll('path.magi-square');
        const magiBadges = magikasSVG.querySelectorAll('.magi-badge');

        //console.log('Found', magiSquarePaths.length, 'paths with class magi-square');
        //console.log('Found', magiBadges.length, 'badges with class magi-badge');

        // Apply initial status colors
        magiSquarePaths.forEach((path) => {
            const squareId = path.id;
            
            // Skip the connecting lines path
            if (squareId === 'Jooned-no-hover-here') {
                return;
            }

            // Determine property status
            let status = 'available';
            if (PROPERTY_STATUS.booked.includes(squareId)) {
                status = 'booked';
            } else if (PROPERTY_STATUS.sold.includes(squareId)) {
                status = 'sold';
            }

            // Apply initial status color
            const colors = STATUS_COLORS[status];
            path.style.fill = colors.original;
            path.style.opacity = '0.8'; // Slightly more visible for status colors
            
            // Store the status for hover effects
            path.dataset.status = status;
        });

        // Create targeted hover effects for each square-badge pair
        magiSquarePaths.forEach((path) => {
            const squareId = path.id;
            //console.log(`Setting up square with ID: ${squareId}`);

            // Skip the connecting lines path
            if (squareId === 'Jooned-no-hover-here') {
                //console.log('Skipping connecting lines path');
                return;
            }

            // Find the corresponding badge (square ID + '_badge')
            const correspondingBadgeId = squareId + '_badge';
            const correspondingBadge = magikasSVG.querySelector(`#${correspondingBadgeId}`);

            if (correspondingBadge) {
                //console.log(`Found matching badge: ${correspondingBadgeId}`);

                // Get the property status
                const status = path.dataset.status || 'available';
                const colors = STATUS_COLORS[status];

                // Square hover effects
                path.addEventListener('mouseenter', function () {
                    //console.log(`Hover started on square: ${squareId} (${status})`);

                    // Fill the square with status hover color
                    this.style.fill = colors.hover;
                    this.style.stroke = colors.stroke;
                    this.style.opacity = '0.9';

                    // Make only the corresponding badge hover up
                    correspondingBadge.classList.add('hover-up');
                });

                path.addEventListener('mouseleave', function () {
                    //console.log(`Hover ended on square: ${squareId}`);

                    // Restore status color (not original brown)
                    this.style.fill = colors.original;
                    this.style.opacity = '0.8';

                    // Return the corresponding badge to normal position
                    correspondingBadge.classList.remove('hover-up');
                });

                // Badge hover effects (badge affects itself and its square)
                correspondingBadge.addEventListener('mouseenter', function () {
                    //console.log(`Badge hovered: ${correspondingBadgeId}`);

                    // Make the badge hover up
                    this.classList.add('hover-up');

                    // Also highlight the corresponding square with status hover color
                    path.style.fill = colors.hover;
                    path.style.stroke = colors.stroke;
                    path.style.opacity = '0.9';
                });

                correspondingBadge.addEventListener('mouseleave', function () {
                    //console.log(`Badge unhovered: ${correspondingBadgeId}`);

                    // Return badge to normal position
                    this.classList.remove('hover-up');

                    // Return square to status color
                    path.style.fill = colors.original;
                    path.style.opacity = '0.8';
                });
            } else {
                //console.log(`No matching badge found for square: ${squareId}`);
            }
        });
    }
});