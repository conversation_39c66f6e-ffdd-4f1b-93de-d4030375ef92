/**
 * SVG Table Interaction Script
 * Handles hover effects on table rows and click-to-scroll from SVG squares to table rows
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize the functionality
    initSvgTableInteraction();
    
    // Also run the badge update function after a short delay to ensure SVG is fully rendered
    setTimeout(() => {
        updateBadgePricesForBookedProperties();
    }, 100);
    
    function initSvgTableInteraction() {
        // Add hover effects to table rows
        addTableRowHoverEffects();
        
        // Add click handlers to SVG badges
        addSvgSquareClickHandlers();
        
        // Add hover effects to SVG squares (enhance existing ones)
        enhanceSvgSquareHoverEffects();
    }
    
    function addTableRowHoverEffects() {
        // Get all table rows (excluding header rows)
        const tableRows = document.querySelectorAll('table tbody tr:not(:first-child)');
        
        tableRows.forEach(row => {
            // Add hover effect
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#22c55e';
                this.style.transition = 'background-color 0.3s ease';
                this.style.cursor = 'pointer';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
                this.style.transition = '';
            });
            
            // Add click effect
            row.addEventListener('click', function() {
                // Remove highlight from all rows
                tableRows.forEach(r => r.style.backgroundColor = '');
                
                // Highlight clicked row
                this.style.backgroundColor = '#22c55e';
                this.style.transition = 'background-color 0.3s ease';
                
                // Scroll to the row with smooth animation
                this.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
            });
        });
    }
    
    function addSvgSquareClickHandlers() {
        // Get all SVG badges (groups with class 'magi-badge')
        const svgBadges = document.querySelectorAll('.magi-badge');

        svgBadges.forEach(badge => {
            // Add click handler
            badge.addEventListener('click', function() {
                const badgeId = this.id;

                // Convert badge ID to square ID by removing '_badge' suffix
                // e.g., "Magimanni_tee_11_badge" -> "Magimanni_tee_11"
                const squareId = badgeId.replace('_badge', '');

                // Find corresponding table row
                const targetRow = findTableRowBySquareId(squareId);

                if (targetRow) {
                    // Highlight the target row
                    highlightTableRow(targetRow);

                    // Scroll to the table row
                    scrollToTableRow(targetRow);

                    // Add visual feedback to the clicked badge
                    addBadgeClickFeedback(this);
                }
            });
        });
    }
    
    function findTableRowBySquareId(squareId) {
        // Convert SVG ID format to table address format
        // e.g., "Nulu_tee_10" -> "Nulu tee 10"
        const address = convertSquareIdToAddress(squareId);
        
        // Search in both tables
        const tables = document.querySelectorAll('table');
        
        for (let table of tables) {
            const rows = table.querySelectorAll('tbody tr:not(:first-child)');
            
            for (let row of rows) {
                const addressCell = row.querySelector('td:first-child .rv_tb-text');
                if (addressCell && addressCell.textContent.trim() === address) {
                    return row;
                }
            }
        }
        
        return null;
    }
    
    function convertSquareIdToAddress(squareId) {
        // Convert underscore format to readable address
        // e.g., "Nulu_tee_10" -> "Nulu tee 10"
        // e.g., "Magimanni_tee_4" -> "Mägimänni tee 4"
        
        if (squareId.includes('Nulu_tee')) {
            const number = squareId.replace('Nulu_tee_', '');
            return `Nulu tee ${number}`;
        } else if (squareId.includes('Magimanni_tee')) {
            const number = squareId.replace('Magimanni_tee_', '');
            return `Mägimänni tee ${number}`;
        }
        
        return squareId;
    }
    
    function highlightTableRow(row) {
        // Remove highlight from all rows in all tables
        const allRows = document.querySelectorAll('table tbody tr:not(:first-child)');
        allRows.forEach(r => {
            r.style.backgroundColor = '';
            r.style.boxShadow = '';
        });
        
        // Highlight the target row
        row.style.backgroundColor = '#22c55e';
        row.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        row.style.transition = 'all 0.3s ease';
    }
    
    function scrollToTableRow(row) {
        // Scroll to the row with smooth animation
        row.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
        });
    }
    
    function addBadgeClickFeedback(badge) {
        // Add temporary visual feedback to the badge
        const badgePath = badge.querySelector('path');

        if (badgePath) {
            const originalFill = badgePath.style.fill || badgePath.getAttribute('fill');
            const originalOpacity = badgePath.style.fillOpacity || badgePath.getAttribute('fill-opacity');

            // Add clicked class
            badge.classList.add('clicked');

            // Flash effect - make badge brighter
            badgePath.style.fill = '#22c55e';
            badgePath.style.fillOpacity = '0.9';
            badgePath.style.transition = 'all 0.3s ease';

            // Reset after animation
            setTimeout(() => {
                badgePath.style.fill = originalFill;
                badgePath.style.fillOpacity = originalOpacity;
                badgePath.style.transition = '';
                badge.classList.remove('clicked');
            }, 500);
        }
    }

    function addSquareClickFeedback(square) {
        // Add temporary visual feedback
        const originalFill = square.style.fill;
        const originalStroke = square.style.stroke;

        // Add clicked class to remove outline
        square.classList.add('clicked');

        // Flash effect
        square.style.fill = '#ffeb3b';
        square.style.stroke = '#f57f17';
        square.style.strokeWidth = '3';

        // Reset after animation
        setTimeout(() => {
            square.style.fill = originalFill;
            square.style.stroke = originalStroke;
            square.style.strokeWidth = '';
            square.classList.remove('clicked');
        }, 500);
    }
    
    function enhanceSvgSquareHoverEffects() {
        // Enhance existing hover effects for better user experience
        const svgSquares = document.querySelectorAll('.magi-square');
        
        svgSquares.forEach(square => {
            // Add title attribute for tooltip
            const squareId = square.id;
            const address = convertSquareIdToAddress(squareId);
            square.setAttribute('title', `Click to view details for ${address}`);
            
            // Add focus styles for accessibility
            square.addEventListener('focus', function() {
                this.style.outline = '2px solid #2196f3';
                this.style.outlineOffset = '2px';
            });
            
            square.addEventListener('blur', function() {
                this.style.outline = '';
                this.style.outlineOffset = '';
            });
        });
        
        // Update badge prices for booked properties
        updateBadgePricesForBookedProperties();
    }
    
    // Add keyboard navigation support
    function addKeyboardSupport() {
        document.addEventListener('keydown', function(e) {
            // Escape key to clear highlights
            if (e.key === 'Escape') {
                const allRows = document.querySelectorAll('table tbody tr:not(:first-child)');
                allRows.forEach(row => {
                    row.style.backgroundColor = '';
                    row.style.boxShadow = '';
                });
            }
        });
    }
    
    // Initialize keyboard support
    addKeyboardSupport();
    
    // Add some additional styling for better visual feedback
    addCustomStyles();
    
    // Function to update badge prices for booked and sold properties
    function updateBadgePricesForBookedProperties() {
        // Get all SVG squares
        const svgSquares = document.querySelectorAll('.magi-square');

        svgSquares.forEach(square => {
            // Check if the property is booked or sold
            const status = square.getAttribute('data-status');
            const isBooked = status === 'booked';
            const isSold = status === 'sold';

            if (isBooked || isSold) {
                // Find the corresponding badge
                const squareId = square.id;
                const badgeId = `${squareId}_badge`;
                const badge = document.getElementById(badgeId);

                if (badge) {
                    // Find all text elements in the badge (including tspan elements)
                    const textElements = badge.querySelectorAll('text, tspan');

                    textElements.forEach(textElement => {
                        const textContent = textElement.textContent || textElement.innerHTML;

                        // Check if this text contains a price (contains € symbol or numbers followed by €)
                        if (textContent && (textContent.includes('€') || /\d+€/.test(textContent))) {
                            // Find tspan elements inside this text element
                            const tspanElements = textElement.querySelectorAll('tspan');

                            if (tspanElements.length > 0) {
                                // Update the first tspan element (usually the price)
                                const firstTspan = tspanElements[0];

                                // Set text and color based on status
                                if (isBooked) {
                                    firstTspan.textContent = 'BRONEERITUD';
                                    // Adjust x-coordinate to center the longer text
                                    const currentX = parseFloat(firstTspan.getAttribute('x')) || 0;
                                    const newX = currentX - 9; // Adjust by 9 pixels to the left
                                    firstTspan.setAttribute('x', newX.toString());

                                    // Add data attribute for CSS styling
                                    textElement.setAttribute('data-booked', 'true');
                                    firstTspan.setAttribute('data-booked', 'true');

                                    // Yellow color for booked
                                    textElement.style.fill = '#ffeb3b';
                                    firstTspan.style.fill = '#ffeb3b';
                                } else if (isSold) {
                                    firstTspan.textContent = 'MÜÜDUD';
                                    // Adjust x-coordinate to center the text
                                    const currentX = parseFloat(firstTspan.getAttribute('x')) || 0;
                                    const newX = currentX - 3; // Adjust by 3 pixels to the left (shorter text)
                                    firstTspan.setAttribute('x', newX.toString());

                                    // Add data attribute for CSS styling
                                    textElement.setAttribute('data-sold', 'true');
                                    firstTspan.setAttribute('data-sold', 'true');

                                    // Red color for sold
                                    textElement.style.fill = '#ef4444';
                                    firstTspan.style.fill = '#ef4444';
                                }

                                // Common styling for both states
                                textElement.style.fontWeight = '700';
                                textElement.style.fillOpacity = '1';
                                firstTspan.style.fontWeight = '700';
                                firstTspan.style.fillOpacity = '1';

                                // Hide address and square meters text elements in the same badge
                                hideAddressAndSquareMeters(badge);

                                // Force SVG to update
                                textElement.dispatchEvent(new Event('DOMSubtreeModified'));
                            } else {
                                // Fallback: if no tspan, update the text element directly
                                if (isBooked) {
                                    textElement.textContent = 'BRONEERITUD';
                                    textElement.setAttribute('data-booked', 'true');
                                    textElement.style.fill = '#ffeb3b';
                                } else if (isSold) {
                                    textElement.textContent = 'MÜÜDUD';
                                    textElement.setAttribute('data-sold', 'true');
                                    textElement.style.fill = '#ef4444';
                                }

                                // Common styling
                                textElement.style.fontWeight = '700';
                                textElement.style.fillOpacity = '1';
                            }
                        }
                    });
                }
            }
        });

        // Log for debugging
        // console.log('Updated badges for booked and sold properties');
    }
    
    // Function to hide address and square meters text elements
    function hideAddressAndSquareMeters(badge) {
        // Find all text elements in the badge
        const textElements = badge.querySelectorAll('text');
        
        textElements.forEach(textElement => {
            const textContent = textElement.textContent || textElement.innerHTML;
            
            // Check if this is an address text (contains "NULU" or "MÄGIMÄNNI")
            if (textContent && (textContent.includes('NULU') || textContent.includes('MÄGIMÄNNI'))) {
                // Hide the address text
                textElement.style.display = 'none';
                textElement.setAttribute('data-hidden', 'true');
            }
            
            // Check if this is a square meters text (contains "M²")
            if (textContent && textContent.includes('M²')) {
                // Hide the square meters text
                textElement.style.fill = 'transparent';
                textElement.setAttribute('data-hidden', 'true');
            }
            
            // Check if this is a street number text (contains "TEE" followed by a number)
            if (textContent && textContent.includes('TEE') && /\d+/.test(textContent)) {
                // Hide the street number text
                textElement.style.display = 'none';
                textElement.setAttribute('data-hidden', 'true');
            }
        });
    }
    
    function addCustomStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* Enhanced table row hover effects */
            table tbody tr:not(:first-child):hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                background-color: #22c55e !important;
            }
            
            /* Smooth transitions for all interactive elements */
            .magi-square,
            table tbody tr {
                transition: all 0.3s ease;
            }
            
            /* Enhanced SVG square hover */
            .magi-square:hover {
                filter: brightness(1.1);
                transform: scale(1.02);
            }
            
            /* Active/clicked state for table rows */
            table tbody tr.active {
                background-color: #22c55e !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Log initialization
    // console.log('SVG Table Interaction initialized successfully');
});

// Export for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initSvgTableInteraction: initSvgTableInteraction };
}
